#!/usr/bin/env python3
"""
Test script specifically for PLTU Suparma filtering
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_pltu_suparma_filtering():
    """Test filtering for PLTU Suparma power plant."""
    print("🏭 Testing PLTU Suparma Filtering")
    print("=" * 40)
    print("🎯 Goal: Return only 1 relevant source for PLTU Suparma")
    print("🎯 Expected: ptsuparmatbk source with annual report info")
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for PLTU Suparma
        initial_state = initialize_power_plant_state("PLTU Suparma")
        
        # Run the search
        print("\n🔍 Running search for PLTU Suparma...")
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze results
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 PLTU SUPARMA FILTERING RESULTS:")
        print(f"   Total sources: {len(sources)}")
        print(f"   Target: 1 source")
        
        if len(sources) == 1:
            print(f"   🎯 PERFECT! Returned exactly 1 source")
        elif len(sources) <= 3:
            print(f"   ✅ GOOD! Returned {len(sources)} sources")
        else:
            print(f"   ❌ TOO MANY! Returned {len(sources)} sources")
        
        # Analyze each source
        print(f"\n🔍 SOURCE ANALYSIS:")
        
        for i, source in enumerate(sources, 1):
            label = source.get('label', 'Unknown')
            url = source.get('value', '')
            
            print(f"\n   {i}. Label: {label}")
            print(f"      URL: {url[:80]}{'...' if len(url) > 80 else ''}")
            
            # Check if it's PLTU Suparma related
            if any(keyword in label.lower() for keyword in ['ptsuparmatbk', 'suparma', 'pltu']):
                print(f"      ✅ PLTU Suparma related")
            else:
                print(f"      ⚠️  Not clearly PLTU Suparma related")
            
            # Check for annual report indicators
            if any(keyword in url.lower() or keyword in label.lower() 
                   for keyword in ['annual', 'report', 'investor', 'financial']):
                print(f"      ✅ Contains annual report indicators")
            else:
                print(f"      ⚠️  No clear annual report indicators")
        
        # Check for duplicates
        labels = [s.get('label', '') for s in sources]
        unique_labels = set(labels)
        
        print(f"\n📊 DUPLICATE ANALYSIS:")
        print(f"   Unique labels: {len(unique_labels)}")
        print(f"   Total sources: {len(sources)}")
        print(f"   Duplicates: {len(sources) - len(unique_labels)}")
        
        if len(unique_labels) == len(sources):
            print("   ✅ No duplicates found")
        else:
            print("   ❌ Found duplicates - filtering not working properly")
            
            # Show duplicate labels
            from collections import Counter
            label_counts = Counter(labels)
            for label, count in label_counts.items():
                if count > 1:
                    print(f"      - '{label}': {count} times")
        
        # Check final answer
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER ANALYSIS:")
                
                # Check for PLTU Suparma references
                if any(keyword in answer.lower() for keyword in ['pltu', 'suparma']):
                    print("   ✅ Contains PLTU Suparma references")
                else:
                    print("   ⚠️  No PLTU Suparma references found")
                
                # Check for annual report info
                if "annual report" in answer.lower():
                    print("   ✅ Contains annual report information")
                else:
                    print("   ⚠️  No annual report information")
                
                # Check for specific URLs
                if "http" in answer:
                    print("   ✅ Contains website URLs")
                else:
                    print("   ⚠️  No website URLs found")
        
        # Overall assessment
        print(f"\n🏆 OVERALL ASSESSMENT:")
        
        pltu_sources = sum(1 for s in sources 
                          if any(keyword in s.get('label', '').lower() 
                               for keyword in ['ptsuparmatbk', 'suparma', 'pltu']))
        
        print(f"   PLTU Suparma related sources: {pltu_sources}")
        print(f"   Total sources: {len(sources)}")
        print(f"   Duplicates: {len(sources) - len(unique_labels)}")
        
        if len(sources) == 1 and pltu_sources == 1:
            print("   🏆 EXCELLENT! Perfect filtering for PLTU Suparma")
            print("   💡 Ready for PDF scraping")
        elif len(sources) <= 3 and pltu_sources > 0:
            print("   ✅ GOOD! Found relevant sources with minimal duplicates")
        elif pltu_sources > 0:
            print("   ✅ FOUND RELEVANT SOURCES but too many duplicates")
            print("   💡 Deduplication needs improvement")
        else:
            print("   ❌ NO RELEVANT SOURCES FOUND")
            print("   💡 Filtering logic may need adjustment")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}


if __name__ == "__main__":
    test_pltu_suparma_filtering()
