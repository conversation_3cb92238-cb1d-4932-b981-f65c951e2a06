#!/usr/bin/env python3
"""
Test script for ScraperAPI-based PDF scraper

This script tests the ScraperAPI-based PDF scraper functionality.
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables from .env file
load_dotenv()

# Import our ScraperAPI PDF scraper
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper


def test_scraper_api_pdf_scraper(plant_name: str):
    """Test the ScraperAPI-based PDF scraper for a given plant name.
    
    Args:
        plant_name: Name of the power plant to test
    """
    print(f"\n{'='*60}")
    print(f"🧪 TESTING SCRAPER API PDF SCRAPER: {plant_name}")
    print(f"{'='*60}")
    
    # Check for ScraperAPI key
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        print("Please set your ScraperAPI key before running this test.")
        print("Example: export SCRAPER_API_KEY='your-api-key-here'")
        return
    
    try:
        # Initialize the scraper
        scraper = ScraperAPIPDFScraper(download_dir="./test_downloads")
        
        # Test direct PDF search
        print("\n🔍 Testing direct PDF search...")
        current_year = 2024
        from_year = current_year - 2  # Just test the last 3 years to save time
        to_year = current_year
        
        downloaded_files = scraper.run_downloader(plant_name, from_year, to_year)
        
        if downloaded_files:
            print(f"\n✅ Successfully downloaded {len(downloaded_files)} PDF(s):")
            for file_path in downloaded_files:
                print(f"   📄 {file_path}")
        else:
            print("\n⚠️ No PDFs found via direct search.")
            
            # Test URL-based scraping with a sample URL
            print("\n🔍 Testing URL-based scraping...")
            
            # Construct a search query for the company's investor relations page
            query = f'"{plant_name}" investor relations annual report'
            
            # Use the search_pdf_url method to find a relevant URL
            # (We're repurposing this method to find any relevant URL, not just PDFs)
            import requests
            
            params = {
                "api_key": os.getenv("SCRAPER_API_KEY"),
                "query": query,
                "num": 5
            }
            
            try:
                response = requests.get(
                    "https://api.scraperapi.com/structured/google/search", 
                    params=params, 
                    headers={"User-Agent": "Mozilla/5.0"}
                )
                response.raise_for_status()
                results = response.json().get("organic_results", [])
                
                if results:
                    # Try the first few results
                    for i, result in enumerate(results[:3]):
                        url = result.get("link", "")
                        if url.startswith("http"):
                            print(f"\n🔍 Testing URL {i+1}: {url}")
                            page_files = scraper.download_pdfs_from_url(url, plant_name, 3)
                            if page_files:
                                print(f"✅ Found {len(page_files)} PDFs on {url}")
                                downloaded_files.extend(page_files)
                                break
                            else:
                                print(f"❌ No PDFs found on {url}")
                else:
                    print("❌ No search results found.")
            except Exception as e:
                print(f"❌ Error during search: {str(e)}")
        
        # Final results
        if downloaded_files:
            print(f"\n✅ TEST PASSED: Successfully downloaded {len(downloaded_files)} PDF(s)")
            print(f"📁 Files saved in: ./test_downloads/{plant_name.replace(' ', '_')}/")
        else:
            print("\n❌ TEST FAILED: No PDF files were downloaded.")
            
    except Exception as e:
        print(f"❌ Error testing {plant_name}: {str(e)}")


def main():
    """Main test function."""
    print("🧪 ScraperAPI PDF Scraper - Test Suite")
    print("=" * 60)
    
    # Test cases - various types of power plants
    test_cases = [
        "Palo Verde Nuclear Generating Station",
        "Three Mile Island",
        "Diablo Canyon Power Plant"
    ]
    
    print(f"🔍 Running tests for {len(test_cases)} power plants...")
    print("⚠️  Note: Each test may take 1-2 minutes to complete.\n")
    
    for plant_name in test_cases:
        try:
            test_scraper_api_pdf_scraper(plant_name)
            
            # Small delay between tests
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n🛑 Tests interrupted by user.")
            break
        except Exception as e:
            print(f"❌ Failed to test {plant_name}: {str(e)}")
    
    print("\n" + "="*60)
    print("🧪 TEST COMPLETE")
    print("="*60)


if __name__ == "__main__":
    main()