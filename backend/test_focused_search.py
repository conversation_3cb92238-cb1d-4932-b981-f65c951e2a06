#!/usr/bin/env python3
"""
Test script for the improved focused Power Plant Annual Report Search Engine

This script tests the updated search that returns only the most relevant annual report pages.
"""

import os
import sys
from typing import Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state, print_search_results
from agent.graph import power_plant_graph


def test_focused_search(plant_name: str) -> Dict[str, Any]:
    """Test the focused power plant search for a given plant name.
    
    Args:
        plant_name: Name of the power plant to test
        
    Returns:
        Final state from the search
    """
    print(f"\n{'='*60}")
    print(f"🎯 FOCUSED TEST: {plant_name}")
    print(f"{'='*60}")
    
    try:
        # Initialize state
        initial_state = initialize_power_plant_state(plant_name)
        
        # Run the search
        final_state = power_plant_graph.invoke(initial_state)
        
        # Print results
        print_search_results(final_state)
        
        # Analyze the results
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 ANALYSIS:")
        print(f"   Sources returned: {len(sources)}")
        print(f"   Target: 1 primary page or max 5 focused pages")
        
        if len(sources) <= 5:
            print(f"   ✅ Good! Returned {len(sources)} focused sources")
        else:
            print(f"   ⚠️  Too many sources ({len(sources)}), should be ≤ 5")
        
        # Check for duplicate domains
        domains = set()
        duplicates = 0
        for source in sources:
            domain = source.get('value', '').split('/')[2] if '/' in source.get('value', '') else ''
            if domain in domains:
                duplicates += 1
            domains.add(domain)
        
        if duplicates == 0:
            print(f"   ✅ No duplicate domains")
        else:
            print(f"   ⚠️  Found {duplicates} duplicate domains")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error testing {plant_name}: {str(e)}")
        return {}


def main():
    """Main test function for focused search."""
    print("🎯 Power Plant Annual Report Search Engine - Focused Search Test")
    print("=" * 70)
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running tests.")
        return
    
    # Test cases - focus on companies known to have clear annual report pages
    test_cases = [
        "SEIL",  # The example you provided
        "Exelon Corporation",  # Large utility company
        "Duke Energy",  # Major power company
        "Southern Company",  # Well-known utility
        "NextEra Energy"  # Large energy company
    ]
    
    print(f"🔍 Testing focused search for {len(test_cases)} entities...")
    print("🎯 Goal: Return 1 primary page or max 5 focused pages per search\n")
    
    results = {}
    total_sources = 0
    successful_tests = 0
    
    for plant_name in test_cases:
        try:
            result = test_focused_search(plant_name)
            results[plant_name] = result
            
            sources_count = len(result.get("sources_gathered", []))
            total_sources += sources_count
            
            if sources_count > 0 and sources_count <= 5:
                successful_tests += 1
            
            # Small delay between tests
            import time
            time.sleep(3)
            
        except KeyboardInterrupt:
            print("\n🛑 Tests interrupted by user.")
            break
        except Exception as e:
            print(f"❌ Failed to test {plant_name}: {str(e)}")
            results[plant_name] = {"error": str(e)}
    
    # Print summary
    print("\n" + "="*70)
    print("📊 FOCUSED SEARCH TEST SUMMARY")
    print("="*70)
    
    avg_sources = total_sources / len([r for r in results.values() if not r.get("error")]) if results else 0
    
    for plant_name, result in results.items():
        if result and not result.get("error"):
            sources_count = len(result.get("sources_gathered", []))
            status = "✅ FOCUSED" if sources_count <= 5 else "⚠️  TOO MANY"
            print(f"{status} - {plant_name}: {sources_count} sources")
        else:
            print(f"❌ FAILED - {plant_name}: {result.get('error', 'Unknown error')}")
    
    print(f"\n🎯 Results Summary:")
    print(f"   Successful tests: {successful_tests}/{len(test_cases)}")
    print(f"   Average sources per test: {avg_sources:.1f}")
    print(f"   Target: ≤ 5 sources per test")
    
    if successful_tests > 0 and avg_sources <= 5:
        print("\n✅ Focused search is working well!")
        print("💡 The search now returns focused, relevant annual report pages.")
    else:
        print("\n⚠️  Search may need further refinement.")
        print("   Consider adjusting the filtering logic or prompts.")


if __name__ == "__main__":
    main()
