"""
Content-Based URL Analyzer for Power Plant Annual Reports

This module analyzes the actual content of webpages to determine if they contain
annual reports, rather than just relying on URL patterns or labels.
"""

import os
import re
import requests
from typing import List, Dict, Optional, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from dotenv import load_dotenv

load_dotenv()
SCRAPER_API_KEY = os.getenv("SCRAPER_API_KEY")


class ContentAnalyzer:
    """Analyzes webpage content to find annual report pages and PDFs."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def analyze_url_content(self, url: str, use_scraper_api: bool = False) -> Dict[str, any]:
        """Analyze the content of a URL to determine if it contains annual reports.
        
        Args:
            url: URL to analyze
            use_scraper_api: Whether to use ScraperAPI for JavaScript-heavy sites
            
        Returns:
            Dictionary with analysis results
        """
        print(f"🔍 Analyzing content of: {url}")
        
        try:
            # Get page content
            if use_scraper_api and SCRAPER_API_KEY:
                content = self._get_content_via_scraper_api(url)
            else:
                content = self._get_content_direct(url)
            
            if not content:
                return {"relevant": False, "reason": "Could not fetch content"}
            
            # Parse content
            soup = BeautifulSoup(content, 'html.parser')
            
            # Analyze content for annual report indicators
            analysis = self._analyze_content(soup, url)
            
            # Find PDF links if this looks like an annual report page
            if analysis["relevant"]:
                pdf_links = self._find_pdf_links(soup, url)
                analysis["pdf_links"] = pdf_links
                analysis["pdf_count"] = len(pdf_links)
            else:
                analysis["pdf_links"] = []
                analysis["pdf_count"] = 0
            
            return analysis
            
        except Exception as e:
            print(f"❌ Error analyzing {url}: {str(e)}")
            return {"relevant": False, "reason": f"Analysis error: {str(e)}"}
    
    def _get_content_via_scraper_api(self, url: str) -> Optional[str]:
        """Get webpage content using ScraperAPI for JavaScript-heavy sites."""
        if not SCRAPER_API_KEY:
            print("⚠️ ScraperAPI key not available, falling back to direct request")
            return self._get_content_direct(url)
        
        try:
            scraper_url = "http://api.scraperapi.com"
            params = {
                'api_key': SCRAPER_API_KEY,
                'url': url,
                'render': 'true',  # Enable JavaScript rendering
                'wait': 3000,      # Wait 3 seconds for JS to load
            }
            
            print(f"   📡 Using ScraperAPI with JavaScript rendering...")
            response = requests.get(scraper_url, params=params, timeout=30)
            response.raise_for_status()
            
            print(f"   ✅ Got {len(response.text)} characters via ScraperAPI")
            return response.text
            
        except Exception as e:
            print(f"   ❌ ScraperAPI failed: {str(e)}, trying direct request...")
            return self._get_content_direct(url)
    
    def _get_content_direct(self, url: str) -> Optional[str]:
        """Get webpage content using direct HTTP request."""
        try:
            print(f"   📡 Direct HTTP request...")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            print(f"   ✅ Got {len(response.text)} characters directly")
            return response.text
            
        except Exception as e:
            print(f"   ❌ Direct request failed: {str(e)}")
            return None
    
    def _analyze_content(self, soup: BeautifulSoup, url: str) -> Dict[str, any]:
        """Analyze webpage content for annual report indicators."""
        
        # Get all text content
        page_text = soup.get_text().lower()
        page_title = soup.title.string.lower() if soup.title else ""
        
        # Strong indicators (high confidence)
        strong_indicators = [
            'annual report', 'annual reports', 'yearly report', 'yearly reports',
            'form 10-k', '10-k filing', 'sec filing', 'sec filings',
            'financial statements', 'financial reports', 'investor relations',
            'annual financial report', 'corporate annual report'
        ]
        
        # Medium indicators (medium confidence)
        medium_indicators = [
            'investor', 'financial', 'earnings', 'quarterly report',
            'sustainability report', 'corporate report', 'governance',
            'shareholder', 'financial performance', 'financial information'
        ]
        
        # Year indicators (recent years)
        year_indicators = ['2024', '2023', '2022', '2021', '2020', '2019']
        
        # PDF indicators
        pdf_indicators = ['pdf', 'download', 'view report', 'read report']
        
        # Calculate relevance score
        score = 0
        found_indicators = []
        
        # Check strong indicators (worth 10 points each)
        for indicator in strong_indicators:
            if indicator in page_text or indicator in page_title:
                score += 10
                found_indicators.append(f"Strong: {indicator}")
        
        # Check medium indicators (worth 3 points each)
        for indicator in medium_indicators:
            if indicator in page_text or indicator in page_title:
                score += 3
                found_indicators.append(f"Medium: {indicator}")
        
        # Check year indicators (worth 2 points each)
        for year in year_indicators:
            if year in page_text:
                score += 2
                found_indicators.append(f"Year: {year}")
        
        # Check PDF indicators (worth 2 points each)
        for indicator in pdf_indicators:
            if indicator in page_text:
                score += 2
                found_indicators.append(f"PDF: {indicator}")
        
        # URL path analysis (bonus points)
        url_lower = url.lower()
        if any(keyword in url_lower for keyword in ['investor', 'financial', 'annual', 'report']):
            score += 5
            found_indicators.append("URL: Contains relevant keywords")
        
        # Determine relevance
        is_relevant = score >= 10  # Threshold for relevance
        
        confidence = "high" if score >= 20 else "medium" if score >= 10 else "low"
        
        print(f"   📊 Content analysis score: {score} ({confidence} confidence)")
        if found_indicators:
            print(f"   🔍 Found indicators: {', '.join(found_indicators[:5])}...")
        
        return {
            "relevant": is_relevant,
            "score": score,
            "confidence": confidence,
            "indicators": found_indicators,
            "reason": f"Content analysis score: {score} ({'relevant' if is_relevant else 'not relevant'})"
        }
    
    def _find_pdf_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """Find all PDF links on the page."""
        pdf_links = []
        
        # Find all links
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            text = link.get_text(strip=True)
            
            # Check if it's a PDF link
            if self._is_pdf_link(href, text):
                # Convert to absolute URL
                absolute_url = urljoin(base_url, href)
                
                pdf_info = {
                    "url": absolute_url,
                    "text": text,
                    "filename": self._extract_filename(absolute_url, text)
                }
                pdf_links.append(pdf_info)
        
        print(f"   📄 Found {len(pdf_links)} PDF links")
        return pdf_links
    
    def _is_pdf_link(self, href: str, text: str) -> bool:
        """Check if a link is likely a PDF."""
        href_lower = href.lower()
        text_lower = text.lower()
        
        # Direct PDF links
        if href_lower.endswith('.pdf'):
            return True
        
        # Links that mention PDF
        if 'pdf' in href_lower or 'pdf' in text_lower:
            return True
        
        # Links with annual report keywords
        annual_keywords = ['annual', 'report', 'financial', 'yearly']
        if any(keyword in text_lower for keyword in annual_keywords):
            return True
        
        return False
    
    def _extract_filename(self, url: str, text: str) -> str:
        """Extract a meaningful filename for the PDF."""
        # Try to get filename from URL
        parsed = urlparse(url)
        url_filename = os.path.basename(parsed.path)
        
        if url_filename and '.' in url_filename:
            return url_filename
        
        # Generate from text
        if text:
            clean_text = re.sub(r'[^\w\s-]', '', text).strip()
            clean_text = re.sub(r'\s+', '_', clean_text)
            return f"{clean_text}.pdf"
        
        return "annual_report.pdf"
    
    def analyze_multiple_urls(self, urls: List[str], use_scraper_api: bool = False) -> List[Dict[str, any]]:
        """Analyze multiple URLs and return them sorted by relevance."""
        results = []
        
        for url in urls:
            analysis = self.analyze_url_content(url, use_scraper_api)
            analysis["url"] = url
            results.append(analysis)
        
        # Sort by relevance score (highest first)
        results.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        return results
    
    def get_best_annual_report_urls(self, urls: List[str], max_results: int = 3) -> List[str]:
        """Get the best annual report URLs from a list of candidates."""
        print(f"\n🔍 Analyzing {len(urls)} URLs for annual report content...")
        
        # First try without ScraperAPI (faster)
        results = self.analyze_multiple_urls(urls, use_scraper_api=False)
        
        # If no good results, try with ScraperAPI for JavaScript sites
        relevant_results = [r for r in results if r["relevant"]]
        if not relevant_results:
            print("⚠️ No relevant content found with direct requests, trying ScraperAPI...")
            results = self.analyze_multiple_urls(urls[:5], use_scraper_api=True)  # Limit to 5 for API costs
            relevant_results = [r for r in results if r["relevant"]]
        
        # Return the best URLs
        best_urls = [r["url"] for r in relevant_results[:max_results]]
        
        print(f"\n📊 Content Analysis Summary:")
        for i, result in enumerate(results[:5], 1):
            status = "✅ RELEVANT" if result["relevant"] else "❌ Not relevant"
            print(f"   {i}. {status} (score: {result.get('score', 0)}) - {result['url'][:60]}...")
        
        return best_urls
