"""
PDF Scraper Module for Power Plant Annual Reports

This module provides functionality to scrape PDF annual reports from
identified investor relations and financial statements pages using Selenium.
"""

import os
import time
import requests
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


def resolve_redirect_url(redirect_url: str) -> str:
    """Resolve Google Search API redirect URLs to actual URLs.

    Args:
        redirect_url: The redirect URL from Google Search API

    Returns:
        The actual resolved URL, or the original URL if resolution fails
    """
    if "grounding-api-redirect" not in redirect_url:
        return redirect_url

    try:
        # Make a HEAD request to get the final URL without downloading content
        response = requests.head(redirect_url, allow_redirects=True, timeout=10)
        return response.url
    except Exception:
        # If resolution fails, return the original URL
        return redirect_url


class PDFScraper:
    """Class to handle PDF scraping from annual report pages."""
    
    def __init__(self, download_dir: str = "./annual_reports", headless: bool = True):
        """Initialize the PDF scraper.
        
        Args:
            download_dir: Directory to save downloaded PDFs
            headless: Whether to run browser in headless mode
        """
        self.download_dir = os.path.abspath(download_dir)
        self.headless = headless
        self.driver = None
        
        # Create download directory if it doesn't exist
        os.makedirs(self.download_dir, exist_ok=True)
    
    def setup_driver(self) -> webdriver.Chrome:
        """Set up Chrome WebDriver with appropriate options.
        
        Returns:
            Configured Chrome WebDriver instance
        """
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # Set download preferences
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "plugins.always_open_pdf_externally": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Additional options for stability
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            raise WebDriverException(f"Failed to initialize Chrome driver: {str(e)}")
    
    def find_pdf_links(self, url: str) -> List[Dict[str, str]]:
        """Find all PDF links on a given webpage.
        
        Args:
            url: URL of the webpage to scrape
            
        Returns:
            List of dictionaries containing PDF link information
        """
        if not self.driver:
            self.driver = self.setup_driver()
        
        pdf_links = []
        
        try:
            print(f"🔍 Scanning {url} for PDF links...")
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find all links that might be PDFs
            links = self.driver.find_elements(By.TAG_NAME, "a")
            print(f"   Found {len(links)} total links on the page")

            pdf_count = 0
            for link in links:
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()

                    # Debug: Show all PDF-like links
                    if href and ('.pdf' in href.lower() or 'pdf' in href.lower()):
                        pdf_count += 1
                        print(f"   PDF link {pdf_count}: {href[:80]}... | Text: '{text[:30]}...'")

                        if self._is_likely_annual_report_pdf(href, text):
                            # Convert relative URLs to absolute
                            absolute_url = urljoin(url, href)

                            pdf_info = {
                                "url": absolute_url,
                                "text": text,
                                "filename": self._extract_filename(absolute_url, text)
                            }
                            pdf_links.append(pdf_info)
                            print(f"      ✅ Accepted as annual report PDF")
                        else:
                            print(f"      ❌ Rejected - not annual report related")

                except Exception as e:
                    continue  # Skip problematic links

            if pdf_count == 0:
                print("   ⚠️  No PDF links found on this page")
                print("   💡 The page might use JavaScript to load PDFs or have a different structure")
            
            print(f"📄 Found {len(pdf_links)} potential annual report PDFs")
            
        except TimeoutException:
            print(f"⚠️  Timeout loading {url}")
        except Exception as e:
            print(f"❌ Error scanning {url}: {str(e)}")
        
        return pdf_links
    
    def _is_likely_annual_report_pdf(self, href: str, text: str) -> bool:
        """Check if a link is likely an annual report PDF.

        Args:
            href: The href attribute of the link
            text: The visible text of the link

        Returns:
            True if the link is likely an annual report PDF
        """
        # Check if URL ends with .pdf or contains pdf
        href_lower = href.lower()
        if not (href_lower.endswith('.pdf') or 'pdf' in href_lower):
            return False

        # Keywords that indicate annual reports
        annual_report_keywords = [
            'annual report', 'annual', 'yearly report', 'form 10-k', '10-k',
            'financial report', 'financial statement', 'investor report',
            'sustainability report', 'corporate report', '2024', '2023', '2022', '2021', '2020'
        ]

        # Check both URL and link text for keywords
        combined_text = f"{href} {text}".lower()

        # More flexible matching - if it's a PDF and has any relevant keywords
        has_keywords = any(keyword in combined_text for keyword in annual_report_keywords)

        # Also accept PDFs that are clearly in annual report sections
        in_annual_section = any(section in href_lower for section in [
            'annual', 'investor', 'financial', 'report'
        ])

        return has_keywords or in_annual_section
    
    def _extract_filename(self, url: str, text: str) -> str:
        """Extract a meaningful filename for the PDF.
        
        Args:
            url: PDF URL
            text: Link text
            
        Returns:
            Suggested filename for the PDF
        """
        # Try to get filename from URL
        parsed_url = urlparse(url)
        url_filename = os.path.basename(parsed_url.path)
        
        if url_filename and url_filename.endswith('.pdf'):
            return url_filename
        
        # Generate filename from text
        if text:
            # Clean the text to make it a valid filename
            clean_text = "".join(c for c in text if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_text = clean_text.replace(' ', '_')
            return f"{clean_text}.pdf"
        
        # Fallback filename
        return "annual_report.pdf"
    
    def download_pdf(self, pdf_info: Dict[str, str], plant_name: str) -> Optional[str]:
        """Download a PDF file.

        Args:
            pdf_info: Dictionary containing PDF information
            plant_name: Name of the power plant (for organizing files)

        Returns:
            Path to downloaded file if successful, None otherwise
        """
        try:
            url = pdf_info["url"]
            filename = pdf_info["filename"]

            # Create plant-specific directory
            plant_dir = os.path.join(self.download_dir, self._sanitize_filename(plant_name))
            os.makedirs(plant_dir, exist_ok=True)

            # Full path for the downloaded file
            file_path = os.path.join(plant_dir, filename)

            # Check if file already exists
            if os.path.exists(file_path):
                print(f"⏭️  Skipping (already exists): {filename}")
                return file_path

            print(f"⬇️  Downloading: {filename}")

            # Download the file
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            print(f"✅ Downloaded: {file_path}")
            return file_path

        except Exception as e:
            print(f"❌ Failed to download {pdf_info.get('filename', 'PDF')}: {str(e)}")
            return None
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize a string to be used as a filename.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename safe for filesystem use
        """
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove extra spaces and limit length
        filename = filename.strip()[:100]
        
        return filename
    
    def scrape_annual_reports(self, urls: List[str], plant_name: str) -> List[str]:
        """Scrape annual report PDFs from a list of URLs.

        Args:
            urls: List of URLs to scrape for PDFs (may include redirect URLs)
            plant_name: Name of the power plant

        Returns:
            List of paths to downloaded PDF files
        """
        downloaded_files = []

        try:
            self.driver = self.setup_driver()

            for url in urls:
                # Resolve redirect URLs to actual URLs
                resolved_url = resolve_redirect_url(url)
                print(f"\n🔍 Searching for PDFs on: {resolved_url}")
                if resolved_url != url:
                    print(f"   (Resolved from redirect: {url[:50]}...)")

                # Find PDF links on this page
                pdf_links = self.find_pdf_links(resolved_url)

                # Download each PDF
                for pdf_info in pdf_links:
                    file_path = self.download_pdf(pdf_info, plant_name)
                    if file_path:
                        downloaded_files.append(file_path)

                # Small delay between requests
                time.sleep(2)

        finally:
            self.cleanup()

        return downloaded_files
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass
            self.driver = None
