#!/usr/bin/env python3
"""
Power Plant Annual Report Search Terminal Interface

This script provides a simple terminal interface to test the power plant
annual report search functionality without requiring the frontend.
"""

import os
import sys
import requests
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from agent.graph import power_plant_graph
from agent.configuration import Configuration
from agent.pdf_scraper import PDFScraper
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper
from agent.content_analyzer import ContentAnalyzer


def initialize_power_plant_state(plant_name: str) -> Dict[str, Any]:
    """Initialize the state for power plant annual report search.
    
    Args:
        plant_name: Name of the power plant to search for
        
    Returns:
        Dictionary containing the initial state for the search
    """
    return {
        "messages": [HumanMessage(content=plant_name)],
        "plant_name": plant_name,
        "search_phase": "direct_search",
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "annual_report_urls": [],
        "initial_search_query_count": 2,
        "max_research_loops": 2,
        "research_loop_count": 0,
        "reasoning_model": "gemini-2.5-flash",
        "holding_company_name": "",
        "found_annual_reports": False,
    }


def print_separator():
    """Print a visual separator for better output formatting."""
    print("\n" + "="*80 + "\n")


def print_search_results(final_state: Dict[str, Any]):
    """Print the final search results in a formatted way.

    Args:
        final_state: The final state from the graph execution
    """
    # Check if final_state is None or empty
    if not final_state:
        print_separator()
        print("❌ No search results available")
        print_separator()
        return
        
    print_separator()
    print("🔍 POWER PLANT ANNUAL REPORT SEARCH RESULTS")
    print_separator()

    plant_name = final_state.get("plant_name", "Unknown")
    print(f"Power Plant: {plant_name}")

    holding_company = final_state.get("holding_company_name", "")
    if holding_company:
        print(f"Holding Company: {holding_company}")

    search_phase = final_state.get("search_phase", "direct_search")
    print(f"Search Phase: {search_phase.replace('_', ' ').title()}")

    print_separator()

    # Check if the results are likely incorrect (mismatch between search and results)
    result_mismatch = False
    result_content = ""
    
    # Print the final answer
    if final_state.get("messages"):
        try:
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                result_content = final_message.content
                print("📄 ANNUAL REPORT FINDINGS:")
                print(result_content)
                
                # Check for potential mismatch between plant name and results
                # This helps detect when search returns completely unrelated results
                plant_name_lower = plant_name.lower()
                content_lower = result_content.lower()
                
                # List of known incorrect matches to check for
                incorrect_matches = {
                    "pirkey": ["seil", "sembcorp", "tanweer"],
                    "san miguel": ["sembcorp", "seil", "tanweer"]
                }
                
                # Check if we have a known incorrect match
                if plant_name_lower in incorrect_matches:
                    for incorrect_term in incorrect_matches[plant_name_lower]:
                        if incorrect_term in content_lower:
                            result_mismatch = True
                            print("\n⚠️  WARNING: Search results appear to be for a different power plant!")
                            print(f"   Requested: {plant_name}")
                            print(f"   But found information about: {incorrect_term.title()}")
                            break
                
                # Generic mismatch detection - if plant name not in content at all
                if not result_mismatch and plant_name_lower not in content_lower:
                    # Check if any word from plant name is in content
                    plant_words = plant_name_lower.split()
                    if not any(word in content_lower for word in plant_words if len(word) > 3):
                        result_mismatch = True
                        print("\n⚠️  WARNING: Search results may not match requested power plant!")
                        print(f"   Requested: {plant_name}")
                        print("   Results don't mention this power plant")
                
        except (IndexError, AttributeError) as e:
            print("❌ Error displaying results: No valid content found")
            print(f"Error details: {str(e)}")

    print_separator()

    # Print sources found
    sources = final_state.get("sources_gathered", [])
    if sources:
        print("🔗 SOURCES FOUND:")
        for i, source in enumerate(sources, 1):
            print(f"{i}. {source.get('label', 'Unknown Source')}")
            print(f"   URL: {source.get('value', 'No URL')}")
            print()
    else:
        print("❌ No sources found")
    
    # If we detected a mismatch, suggest using direct search instead
    if result_mismatch:
        print("\n⚠️  The search results don't appear to match your request.")
        print("   This can happen when the search engine returns incorrect results.")
        print("   Recommend using direct PDF search instead.")
        
    print_separator()
    
    # Return whether there was a mismatch (for use by the calling function)
    return result_mismatch


def scrape_annual_report_pdfs(sources: List[Dict[str, Any]], plant_name: str, final_state: Dict[str, Any] = None):
    """Scrape PDF annual reports from the identified sources.

    Args:
        sources: List of source dictionaries with URLs
        plant_name: Name of the power plant
        final_state: Final state containing the AI's answer with URLs
    """
    print("🤖 Starting PDF scraping process...")
    print("⚠️  Using ScraperAPI for more reliable PDF downloading...")

    try:
        # Check if SCRAPER_API_KEY is set
        if not os.getenv("SCRAPER_API_KEY"):
            print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
            print("Please set your ScraperAPI key before running this script.")
            print("Example: export SCRAPER_API_KEY='your-api-key-here'")
            return

        # Extract URLs from sources
        source_urls = [source.get('value', '') for source in sources if source.get('value')]
        source_urls = [url for url in source_urls if url.startswith('http')]

        # PRIORITIZE URLs from the final answer (AI-identified URLs are more accurate)
        answer_urls = []
        if final_state and final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content

                # Extract URLs from the answer using regex
                import re
                found_urls = re.findall(r'https?://[^\s\)\]\*]+', answer)

                # Clean up URLs (remove trailing punctuation and markdown)
                clean_urls = []
                for url in found_urls:
                    # Remove trailing punctuation and markdown
                    url = url.rstrip('.,;:!?)*')
                    # Remove markdown formatting
                    url = url.replace('**', '')
                    if url.startswith('http') and not 'grounding-api-redirect' in url:
                        clean_urls.append(url)
                        print(f"   🎯 AI-identified URL: {url}")

                answer_urls = clean_urls

                # If we found AI URLs, prioritize them over redirect URLs
                if answer_urls:
                    print(f"   ✅ Found {len(answer_urls)} AI-identified URLs - these are more reliable!")
                else:
                    print(f"   ⚠️  No direct URLs found in AI answer, will use redirect URLs")

        # PRIORITIZE AI URLs over redirect URLs (AI URLs are more accurate)
        if answer_urls:
            # Use AI-identified URLs first, then add source URLs as backup
            all_urls = answer_urls + [url for url in source_urls if url not in answer_urls]
            print(f"📊 Prioritizing {len(answer_urls)} AI URLs, with {len(source_urls)} redirect URLs as backup")
        else:
            # Fallback to source URLs if no AI URLs found
            all_urls = source_urls
            print(f"📊 Using {len(source_urls)} redirect URLs (no AI URLs found)")

        all_urls = [url for url in all_urls if url.startswith('http')]

        if not all_urls:
            print("❌ No valid URLs found for scraping.")
            return

        # Step 1: Resolve redirect URLs to get actual URLs
        print("🔍 Resolving redirect URLs...")
        resolved_urls = []

        for i, url in enumerate(all_urls, 1):
            try:
                print(f"   Processing URL {i}/{len(all_urls)}: {url[:60]}...")

                # For redirect URLs, resolve to get the actual URL
                if "grounding-api-redirect" in url:
                    response = requests.head(url, allow_redirects=True, timeout=10)
                    resolved_url = response.url
                    print(f"      Resolved to: {resolved_url}")
                else:
                    resolved_url = url
                    print(f"      Direct URL: {resolved_url}")

                resolved_urls.append(resolved_url)

            except Exception as e:
                print(f"   ❌ Could not resolve URL: {url[:50]}... ({str(e)})")
                # If resolution fails, try to use the original URL
                if "grounding-api-redirect" not in url:
                    resolved_urls.append(url)
                    print(f"   ⚠️  Using original URL as fallback")
                continue

        # Step 2: Use content analyzer to find the best annual report URLs
        print(f"\n🧠 Analyzing content of {len(resolved_urls)} URLs...")
        content_analyzer = ContentAnalyzer()
        best_urls = content_analyzer.get_best_annual_report_urls(resolved_urls, max_results=3)

        if not best_urls:
            print("⚠️ No relevant annual report content found, using original URLs as fallback")
            # Deduplicate by domain as fallback
            unique_urls = []
            seen_domains = set()
            for url in resolved_urls:
                domain = url.split('/')[2] if '/' in url else url
                if domain not in seen_domains:
                    unique_urls.append(url)
                    seen_domains.add(domain)
                    if len(unique_urls) >= 3:
                        break
        else:
            unique_urls = best_urls
            print(f"✅ Selected {len(unique_urls)} best URLs based on content analysis")

        if not unique_urls:
            print("❌ No unique URLs found after deduplication.")
            return

        print(f"\n📊 Will scrape {len(unique_urls)} unique URL(s) for all PDFs")
        print("🔍 Using ScraperAPI to comprehensively scrape all PDFs from these websites...")

        # Show the URLs that will be scraped
        for i, url in enumerate(unique_urls, 1):
            print(f"   {i}. {url}")

        # Initialize ScraperAPI scraper
        scraper_api = ScraperAPIPDFScraper(download_dir="./annual_reports")
        downloaded_files = []

        # Scrape each URL comprehensively for all PDFs
        for i, url in enumerate(unique_urls, 1):
            print(f"\n🔍 Scraping website {i}/{len(unique_urls)}: {url}")
            print(f"   📄 Looking for ALL PDFs on this website...")

            # Use ScraperAPI to download all PDFs from this URL
            page_files = scraper_api.download_pdfs_from_url(url, plant_name, max_pdfs=10)

            if page_files:
                downloaded_files.extend(page_files)
                print(f"   ✅ Found {len(page_files)} PDFs on this website")
            else:
                print(f"   ⚠️  No PDFs found on this website")

        print(f"\n📊 Total PDFs found across all websites: {len(downloaded_files)}")

        # If ScraperAPI fails, try Selenium as fallback
        if not downloaded_files:
            print("\n🔄 ScraperAPI found no PDFs. Trying Selenium as fallback...")
            try:
                selenium_scraper = PDFScraper(download_dir="./annual_reports", headless=True)
                downloaded_files = selenium_scraper.scrape_annual_reports(unique_urls, plant_name)
                selenium_scraper.cleanup()

                if downloaded_files:
                    print(f"   ✅ Selenium found {len(downloaded_files)} PDFs!")
                else:
                    print(f"   ⚠️  Selenium also found no PDFs")

            except Exception as e:
                print(f"   ❌ Selenium fallback failed: {str(e)}")
        
        # If no PDFs found, try direct search using ScraperAPI
        if not downloaded_files:
            print("\n🔄 No PDFs found on resolved URLs, trying direct search...")
        
            # Try to find PDFs for the last 5 years
            current_year = 2024  # You can use datetime.now().year for current year
            from_year = current_year - 4
            to_year = current_year
            
            additional_files = scraper_api.run_downloader(plant_name, from_year, to_year)
            if additional_files:
                downloaded_files.extend(additional_files)

        # If no PDFs found and we have answer URLs, try some common annual report URL patterns
        if not downloaded_files and answer_urls:
            print("\n🔄 No PDFs found, trying alternative URL patterns...")

            alternative_urls = []
            
            # Special case for San Miguel Corporation
            if 'San Miguel' in plant_name or (final_state and 'San Miguel' in final_state.get('holding_company_name', '')):
                print("🔍 Detected San Miguel Corporation - using specific URL patterns")
                alternatives = [
                    "https://www.sanmiguel.com.ph/investor-relations/annual-reports",
                    "https://www.sanmiguel.com.ph/corporate/investor-relations/annual-reports",
                    "https://www.sanmiguel.com.ph/corporate/investor-relations/financial-performance/annual-reports",
                    "https://www.smc.com.ph/investor-relations/annual-reports/",
                    "https://www.smcglobalpower.com.ph/investor-relations/annual-reports",
                    "https://www.smcglobalpower.com.ph/reports/annual-reports"
                ]
                alternative_urls.extend(alternatives)
            else:
                # Generic patterns based on URLs found
                for url in answer_urls:
                    if 'sanmiguel.com.ph' in url:
                        # Try common San Miguel annual report URL patterns
                        alternatives = [
                            "https://www.sanmiguel.com.ph/investor-relations/annual-reports",
                            "https://www.sanmiguel.com.ph/corporate/investor-relations/annual-reports",
                            "https://www.smc.com.ph/investor-relations/annual-reports/"
                        ]
                        alternative_urls.extend(alternatives)
                    # Add more patterns for other companies as needed

            if alternative_urls:
                print(f"   Trying {len(alternative_urls)} alternative URLs...")
                for alt_url in alternative_urls:
                    if alt_url not in unique_urls:  # Don't repeat URLs we already tried
                        print(f"   🔍 Trying: {alt_url}")
                        try:
                            page_files = scraper_api.download_pdfs_from_url(alt_url, plant_name, 5)
                            if page_files:
                                downloaded_files.extend(page_files)
                                print(f"   ✅ Found {len(page_files)} PDFs on {alt_url}")
                                if len(page_files) >= 3:  # If we found at least 3 PDFs, that's enough
                                    break
                            else:
                                print(f"   ❌ No PDFs found on {alt_url}")
                        except Exception as e:
                            print(f"   ❌ Failed: {str(e)}")
                            continue
                            
            # If still no PDFs found, try direct search for the holding company
            if not downloaded_files and final_state and final_state.get('holding_company_name'):
                holding_company = final_state.get('holding_company_name')
                print(f"\n🔄 Trying direct search for holding company: {holding_company}")
                
                # Try to find PDFs for the last 3 years
                current_year = 2024
                from_year = current_year - 2
                to_year = current_year
                
                additional_files = scraper_api.run_downloader(holding_company, from_year, to_year)
                if additional_files:
                    downloaded_files.extend(additional_files)

        if downloaded_files:
            # Remove duplicates from downloaded files
            unique_files = list(set(downloaded_files))
            print(f"\n✅ Successfully downloaded {len(unique_files)} unique PDF(s):")
            for file_path in unique_files:
                print(f"   📄 {file_path}")
            print(f"\n📁 Files saved in: ./annual_reports/{plant_name.replace(' ', '_')}/")
        else:
            print("\n❌ No PDF files were downloaded.")
            print("   This could be because:")
            print("   - No annual report PDFs were found on the pages")
            print("   - The pages require authentication")
            print("   - The PDFs are embedded or dynamically loaded")

    except ImportError:
        print("❌ Required packages not installed. To enable PDF scraping, install:")
        print("   pip install requests beautifulsoup4 python-dotenv")

    except Exception as e:
        print(f"❌ Error during PDF scraping: {str(e)}")


def main():
    """Main function to run the power plant search interface."""
    print("🏭 Power Plant Annual Report Search Engine")
    print("==========================================")
    print()
    print("This tool searches for annual reports for power plants.")
    print("It will first search directly for the power plant's annual reports,")
    print("and if not found, will search for the holding company's reports.")
    print()
    
    while True:
        try:
            # Get power plant name from user
            plant_name = input("Enter power plant name (or 'quit' to exit): ").strip()
            
            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not plant_name:
                print("❌ Please enter a valid power plant name.")
                continue
            
            print(f"\n🔍 Searching for annual reports for: {plant_name}")
            print("⏳ This may take a few moments...")
            
            # Initialize state
            initial_state = initialize_power_plant_state(plant_name)
            
            # Run the power plant search graph
            try:
                final_state = power_plant_graph.invoke(initial_state)
                
                # Check if final_state is None or missing essential components
                if not final_state or not final_state.get("messages"):
                    print("\n❌ Error: Search returned incomplete results.")
                    print("This could be because:")
                    print("   - No relevant information was found for this power plant")
                    print("   - The search API returned limited or no results")
                    print("   - There was an issue with the search process")
                    use_direct_search = True
                else:
                    # Print search results if we have a valid final state
                    # The function returns True if there's a mismatch between requested plant and results
                    result_mismatch = print_search_results(final_state)
                    
                    # If there's a mismatch, suggest direct search
                    if result_mismatch:
                        use_direct_search_response = input("\n🤖 Would you like to try direct PDF search instead? (y/n): ").strip().lower()
                        use_direct_search = use_direct_search_response in ['y', 'yes']
                    else:
                        use_direct_search = False
                        
                        # If results look good, ask if user wants to scrape PDFs
                        if final_state.get("sources_gathered"):
                            scrape_pdfs = input("\n🤖 Would you like to scrape PDFs from these sources? (y/n): ").strip().lower()
                            if scrape_pdfs in ['y', 'yes']:
                                scrape_annual_report_pdfs(final_state.get("sources_gathered", []), plant_name, final_state)
                
                # If we need to use direct search (due to error, mismatch, or user choice)
                if use_direct_search:
                    print("\n🔍 Searching for potential annual report sources...")

                    # Initialize the scraper for searching (not downloading yet)
                    scraper = ScraperAPIPDFScraper(download_dir="./annual_reports")

                    # Search for potential sources without downloading
                    potential_sources = []

                    # For Pirkey specifically, try with AEP (American Electric Power) as well
                    if plant_name.lower() == "pirkey" or "pirkey" in plant_name.lower():
                        print("\n🔍 Detected Pirkey Power Plant - checking parent company AEP...")
                        potential_sources.extend([
                            "American Electric Power annual reports",
                            "SWEPCO annual reports",
                            f"{plant_name} annual reports"
                        ])
                    else:
                        potential_sources.append(f"{plant_name} annual reports")

                    print(f"\n📋 Found potential sources for annual reports:")
                    for i, source in enumerate(potential_sources, 1):
                        print(f"   {i}. {source}")

                    # Ask for permission before downloading
                    download_permission = input("\n🤖 Would you like to search and download PDFs from these sources? (y/n): ").strip().lower()

                    if download_permission in ['y', 'yes']:
                        print("\n⬇️  Starting PDF download process...")

                        # Create a directory for downloads
                        plant_dir = os.path.join("./annual_reports", plant_name.replace(' ', '_'))
                        os.makedirs(plant_dir, exist_ok=True)

                        downloaded_files = []

                        # For Pirkey specifically, try with AEP (American Electric Power) as well
                        if plant_name.lower() == "pirkey" or "pirkey" in plant_name.lower():
                            print("\n🔍 Downloading from AEP (American Electric Power)...")
                            aep_files = scraper.run_downloader("American Electric Power", 2022, 2024)

                            if aep_files:
                                downloaded_files.extend(aep_files)
                                print(f"\n✅ Downloaded {len(aep_files)} PDF(s) from AEP")
                            else:
                                # If AEP didn't work, try SWEPCO
                                print("\n🔍 Trying SWEPCO (Southwestern Electric Power Company)...")
                                swepco_files = scraper.run_downloader("SWEPCO", 2022, 2024)

                                if swepco_files:
                                    downloaded_files.extend(swepco_files)
                                    print(f"\n✅ Downloaded {len(swepco_files)} PDF(s) from SWEPCO")

                        # Try direct search with the plant name
                        print(f"\n🔍 Searching for {plant_name} annual reports...")
                        current_year = 2024
                        from_year = current_year - 4
                        to_year = current_year

                        plant_files = scraper.run_downloader(plant_name, from_year, to_year)
                        if plant_files:
                            downloaded_files.extend(plant_files)

                        if downloaded_files:
                            # Remove duplicates
                            unique_files = list(set(downloaded_files))
                            print(f"\n✅ Successfully downloaded {len(unique_files)} PDF(s):")
                            for file_path in unique_files:
                                print(f"   📄 {file_path}")
                            print(f"\n📁 Files saved in: ./annual_reports/{plant_name.replace(' ', '_')}/")
                        else:
                            print("\n❌ No PDF files were found through direct search.")
                            print("Try a different power plant name or check your ScraperAPI key.")
                    else:
                        print("\n⏭️  Skipping PDF download. Search completed.")
                
            except Exception as e:
                print(f"\n❌ Error during search: {str(e)}")
                print("Please check your API keys and internet connection.")
                
                # Offer direct PDF search as a fallback option
                print("\n🔍 Main search failed. Would you like to try direct PDF search as fallback?")

                fallback_permission = input("🤖 Search for PDFs directly? (y/n): ").strip().lower()

                if fallback_permission in ['y', 'yes']:
                    try:
                        print("\n⬇️  Starting direct PDF search...")

                        # Create a directory for downloads
                        plant_dir = os.path.join("./annual_reports", plant_name.replace(' ', '_'))
                        os.makedirs(plant_dir, exist_ok=True)

                        # Initialize the scraper
                        scraper = ScraperAPIPDFScraper(download_dir="./annual_reports")

                        downloaded_files = []

                        # For Pirkey specifically, try with AEP (American Electric Power) as well
                        if plant_name.lower() == "pirkey" or "pirkey" in plant_name.lower():
                            print("\n🔍 Detected Pirkey Power Plant - trying with parent company AEP...")

                            # Try AEP first
                            aep_files = scraper.run_downloader("American Electric Power", 2022, 2024)

                            if aep_files:
                                downloaded_files.extend(aep_files)
                                print(f"\n✅ Downloaded {len(aep_files)} PDF(s) from AEP")
                            else:
                                # If AEP didn't work, try SWEPCO
                                print("\n🔍 Trying SWEPCO (Southwestern Electric Power Company)...")
                                swepco_files = scraper.run_downloader("SWEPCO", 2022, 2024)

                                if swepco_files:
                                    downloaded_files.extend(swepco_files)
                                    print(f"\n✅ Downloaded {len(swepco_files)} PDF(s) from SWEPCO")

                        # Try direct search with the plant name
                        print(f"\n🔍 Searching for {plant_name} annual reports...")
                        current_year = 2024
                        from_year = current_year - 4
                        to_year = current_year

                        plant_files = scraper.run_downloader(plant_name, from_year, to_year)
                        if plant_files:
                            downloaded_files.extend(plant_files)

                        if downloaded_files:
                            # Remove duplicates
                            unique_files = list(set(downloaded_files))
                            print(f"\n✅ Successfully downloaded {len(unique_files)} PDF(s):")
                            for file_path in unique_files:
                                print(f"   📄 {file_path}")
                            print(f"\n📁 Files saved in: ./annual_reports/{plant_name.replace(' ', '_')}/")
                        else:
                            print("\n❌ No PDF files were found through direct search.")
                            print("Try a different power plant name or check your ScraperAPI key.")
                    except Exception as fallback_error:
                        print(f"\n❌ Fallback search also failed: {str(fallback_error)}")
                else:
                    print("\n⏭️  Skipping direct PDF search.")
                
                continue
            
            # Ask if user wants to search for another plant
            print("\n" + "-"*50)
            continue_search = input("Search for another power plant? (y/n): ").strip().lower()
            if continue_search not in ['y', 'yes']:
                print("👋 Goodbye!")
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 Search interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            continue


if __name__ == "__main__":
    # Check for required environment variables
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        print("Please set your Gemini API key before running this script.")
        print("Example: export GEMINI_API_KEY='your-api-key-here'")
        sys.exit(1)
    
    # Check for ScraperAPI key (warn but don't exit)
    if not os.getenv("SCRAPER_API_KEY"):
        print("⚠️  Warning: SCRAPER_API_KEY environment variable is not set.")
        print("PDF scraping functionality will be limited without a ScraperAPI key.")
        print("To enable full PDF scraping, set your ScraperAPI key:")
        print("Example: export SCRAPER_API_KEY='your-api-key-here'")
        print()
    
    main()
