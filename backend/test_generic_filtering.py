#!/usr/bin/env python3
"""
Test script to verify generic filtering works for any power plant name
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_generic_filtering():
    """Test generic filtering for different power plant names."""
    print("🌐 Testing Generic Filtering for Multiple Power Plants")
    print("=" * 60)
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    # Test different power plant names
    test_plants = [
        "Mailao",
        "PLTU Suparma", 
        "SEIL",
        "Duke Energy",
        "Exelon"
    ]
    
    for plant_name in test_plants:
        print(f"\n{'='*50}")
        print(f"🏭 Testing: {plant_name}")
        print(f"{'='*50}")
        
        try:
            # Initialize state
            initial_state = initialize_power_plant_state(plant_name)
            
            print(f"🔍 Running search for {plant_name}...")
            print("   (Watch for DEBUG messages)")
            
            # Run the search
            final_state = power_plant_graph.invoke(initial_state)
            
            # Analyze results
            sources = final_state.get("sources_gathered", [])
            
            print(f"\n📊 RESULTS FOR {plant_name}:")
            print(f"   Sources found: {len(sources)}")
            
            # Check if sources match the plant name
            relevant_sources = 0
            wrong_sources = 0
            
            for source in sources:
                label = source.get('label', '').lower()
                
                # Check if source seems related to the plant
                plant_words = plant_name.lower().split()
                is_related = any(word in label for word in plant_words if len(word) > 2)
                
                if is_related:
                    relevant_sources += 1
                    print(f"   ✅ Relevant: {source.get('label', 'Unknown')}")
                else:
                    wrong_sources += 1
                    print(f"   ❌ Wrong: {source.get('label', 'Unknown')} (doesn't match {plant_name})")
            
            # Assessment
            if relevant_sources > 0 and wrong_sources == 0:
                print(f"   🏆 EXCELLENT! All {relevant_sources} sources are relevant")
            elif relevant_sources > 0:
                print(f"   ✅ GOOD! {relevant_sources} relevant, {wrong_sources} wrong")
            elif len(sources) > 0:
                print(f"   ⚠️  MIXED! Found sources but none clearly relevant")
            else:
                print(f"   ❌ NO SOURCES! Filtering too aggressive")
            
            # Check final answer
            if final_state.get("messages"):
                final_message = final_state["messages"][-1]
                if hasattr(final_message, 'content'):
                    answer = final_message.content
                    
                    # Check if answer mentions the correct plant
                    plant_words = plant_name.lower().split()
                    answer_mentions_plant = any(word in answer.lower() for word in plant_words if len(word) > 2)
                    
                    if answer_mentions_plant:
                        print(f"   ✅ Answer mentions {plant_name}")
                    else:
                        print(f"   ⚠️  Answer doesn't clearly mention {plant_name}")
        
        except Exception as e:
            print(f"   ❌ Error testing {plant_name}: {str(e)}")
    
    print(f"\n{'='*60}")
    print("🏆 GENERIC FILTERING TEST SUMMARY")
    print("💡 The system should work for ANY power plant name without hardcoding")
    print("💡 Each plant should get sources relevant to that specific plant")
    print("💡 No plant should get sources from a different plant")


if __name__ == "__main__":
    test_generic_filtering()
