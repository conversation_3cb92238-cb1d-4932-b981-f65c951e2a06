#!/usr/bin/env python3
"""
Test script for aggressive filtering in Power Plant Annual Report Search Engine

This script tests the updated aggressive filtering that should return only 1-3 highly relevant sources.
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_aggressive_filtering():
    """Test the aggressive filtering for SEIL."""
    print("🎯 Testing Aggressive Filtering for SEIL")
    print("=" * 50)
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for SEIL
        initial_state = initialize_power_plant_state("SEIL")
        
        # Run the search
        print("🔍 Running search with aggressive filtering...")
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze results
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 RESULTS:")
        print(f"   Sources returned: {len(sources)}")
        print(f"   Target: 1-3 sources maximum")
        
        if len(sources) <= 3:
            print(f"   ✅ EXCELLENT! Returned {len(sources)} focused sources")
        elif len(sources) <= 5:
            print(f"   ✅ GOOD! Returned {len(sources)} sources")
        else:
            print(f"   ❌ TOO MANY! Returned {len(sources)} sources (should be ≤ 3)")
        
        # Analyze source quality
        print(f"\n🔍 SOURCE ANALYSIS:")
        for i, source in enumerate(sources, 1):
            label = source.get('label', 'Unknown')
            url = source.get('value', '')
            
            print(f"   {i}. {label}")
            
            # Check if it's a third-party site
            if any(site in label.lower() or site in url.lower() 
                   for site in ['tracxn', 'zaubacorp', 'careratings', 'pitchbook']):
                print(f"      ❌ Third-party site (should be filtered out)")
            else:
                print(f"      ✅ Official company source")
            
            # Check for annual report indicators
            if any(keyword in url.lower() 
                   for keyword in ['annual', 'investor', 'financial']):
                print(f"      ✅ Contains annual report indicators")
            else:
                print(f"      ⚠️  No clear annual report indicators")
        
        # Check final answer
        if final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content
                print(f"\n📄 FINAL ANSWER ANALYSIS:")
                
                # Check for the correct URL pattern
                if "seilenergy.com" in answer:
                    print("   ✅ Contains SEIL Energy website")
                else:
                    print("   ⚠️  No SEIL Energy website found")
                
                # Check for specific annual report URL
                if any(pattern in answer.lower() for pattern in 
                       ['annualreportinv', 'annual-report', 'investor-relations']):
                    print("   ✅ Contains specific annual report page")
                else:
                    print("   ⚠️  No specific annual report page mentioned")
                
                # Check if answer is concise
                line_count = len(answer.split('\n'))
                if line_count <= 15:
                    print("   ✅ Concise, focused answer")
                else:
                    print("   ⚠️  Answer might be too verbose")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if len(sources) <= 3 and any('seilenergy' in s.get('label', '').lower() for s in sources):
            print("   ✅ EXCELLENT! Aggressive filtering is working perfectly")
            print("   💡 Ready for production use")
        elif len(sources) <= 5:
            print("   ✅ GOOD! Filtering is working well")
            print("   💡 Minor improvements possible")
        else:
            print("   ❌ NEEDS IMPROVEMENT! Too many sources returned")
            print("   💡 Filtering logic needs adjustment")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return {}


if __name__ == "__main__":
    test_aggressive_filtering()
