#!/usr/bin/env python3
"""
Test script for Pirkey Power Plant PDF scraping

This script tests the PDF scraper specifically for Pirkey Power Plant.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables from .env file
load_dotenv()

# Import our ScraperAPI PDF scraper
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper


def test_pirkey_scraping():
    """Test PDF scraping specifically for Pirkey Power Plant."""
    print("\n" + "="*80)
    print("🧪 TESTING PIRKEY POWER PLANT PDF SCRAPING")
    print("="*80)
    
    # Check for ScraperAPI key
    if not os.getenv("SCRAPER_API_KEY"):
        print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
        print("Please set your ScraperAPI key before running this test.")
        print("Example: export SCRAPER_API_KEY='your-api-key-here'")
        return
    
    # Initialize the scraper
    scraper = ScraperAPIPDFScraper(download_dir="./test_downloads")
    
    # Try different variations of the plant name
    plant_variations = [
        "Pirkey Power Plant",
        "H.W. Pirkey Power Plant",
        "AEP Pirkey Power Plant",
        "American Electric Power Pirkey"
    ]
    
    all_downloaded_files = []
    
    # Try direct search for each variation
    for plant_name in plant_variations:
        print(f"\n🔍 Testing direct search for: {plant_name}")
        
        # Try to find PDFs for the last 5 years
        current_year = 2024
        from_year = current_year - 4
        to_year = current_year
        
        try:
            direct_files = scraper.run_downloader(plant_name, from_year, to_year)
            if direct_files:
                print(f"✅ Found {len(direct_files)} PDFs for {plant_name}")
                all_downloaded_files.extend(direct_files)
            else:
                print(f"❌ No PDFs found for {plant_name}")
        except Exception as e:
            print(f"❌ Error during direct search for {plant_name}: {str(e)}")
    
    # If no PDFs found from direct search, try searching for the holding company
    if not all_downloaded_files:
        print("\n🔍 No PDFs found from direct search, trying holding company...")
        
        # Try different holding companies
        holding_companies = [
            "American Electric Power",
            "AEP",
            "Southwestern Electric Power Company",
            "SWEPCO"
        ]
        
        for company in holding_companies:
            print(f"\n🔍 Testing direct search for holding company: {company}")
            
            # Try to find PDFs for the last 3 years
            current_year = 2024
            from_year = current_year - 2
            to_year = current_year
            
            try:
                company_files = scraper.run_downloader(company, from_year, to_year)
                if company_files:
                    print(f"✅ Found {len(company_files)} PDFs for {company}")
                    all_downloaded_files.extend(company_files)
                    break  # Stop after finding PDFs for one company
                else:
                    print(f"❌ No PDFs found for {company}")
            except Exception as e:
                print(f"❌ Error during direct search for {company}: {str(e)}")
    
    # Try specific URLs that might contain annual reports
    if not all_downloaded_files:
        print("\n🔍 No PDFs found from direct search, trying specific URLs...")
        
        specific_urls = [
            "https://www.aep.com/investors/financialfilingsandreports/annualreportsandproxies",
            "https://www.aep.com/about/businesses/swepco",
            "https://www.swepco.com/about/investors"
        ]
        
        for url in specific_urls:
            print(f"\n🔍 Testing URL: {url}")
            
            try:
                url_files = scraper.download_pdfs_from_url(url, "Pirkey_Power_Plant", 3)
                if url_files:
                    print(f"✅ Found {len(url_files)} PDFs on {url}")
                    all_downloaded_files.extend(url_files)
                else:
                    print(f"❌ No PDFs found on {url}")
            except Exception as e:
                print(f"❌ Error testing {url}: {str(e)}")
    
    # Final results
    if all_downloaded_files:
        # Remove duplicates
        unique_files = list(set(all_downloaded_files))
        print(f"\n✅ TEST PASSED: Successfully downloaded {len(unique_files)} unique PDF(s)")
        print("Downloaded files:")
        for file_path in unique_files:
            print(f"   📄 {file_path}")
    else:
        print("\n❌ TEST FAILED: No PDF files were downloaded.")
        print("This could be because:")
        print("   - The power plant doesn't have publicly available annual reports")
        print("   - The reports are only available through the parent company (AEP)")
        print("   - The search terms need to be adjusted")


if __name__ == "__main__":
    test_pirkey_scraping()