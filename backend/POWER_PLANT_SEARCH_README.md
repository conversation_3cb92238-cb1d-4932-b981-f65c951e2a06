# Power Plant Annual Report Search Engine

A specialized search engine built on LangGraph that finds annual reports for power plants. The system intelligently searches for direct power plant annual reports and falls back to searching for holding company reports when needed.

## Features

- **Intelligent Search Strategy**: First searches directly for power plant annual reports, then falls back to holding company search if needed
- **Automated PDF Discovery**: Identifies and extracts links to annual report PDFs from investor relations pages
- **PDF Scraping**: Uses Selenium to automatically download annual report PDFs
- **Terminal Interface**: Simple command-line interface for testing without frontend dependencies
- **Structured Output**: Returns specific website URLs where annual report PDFs can be found

## How It Works

1. **Input**: User provides a power plant name
2. **Primary Search**: Searches for "{power plant name} annual reports"
3. **Website Analysis**: Looks for investor relations, financial statements, or annual report sections
4. **Fallback Strategy**: If no direct reports found, searches for "{power plant name} power plant holding company"
5. **Holding Company Search**: Searches for "{holding company name} annual reports"
6. **PDF Extraction**: Identifies downloadable PDF annual reports
7. **Output**: Provides website links and optionally downloads PDFs using Selenium

## Installation

### Prerequisites

1. **Python 3.8+**
2. **Google Gemini API Key**
3. **Chrome Browser** (for PDF scraping)

### Setup

1. **Install base dependencies**:
   ```bash
   cd backend
   pip install -r pyproject.toml
   ```

2. **Install additional dependencies for power plant search**:
   ```bash
   pip install -r requirements-power-plant.txt
   ```

3. **Install Chrome WebDriver**:
   - **macOS**: `brew install chromedriver`
   - **Ubuntu/Debian**: `sudo apt-get install chromium-browser chromium-chromedriver`
   - **Windows**: Download from [ChromeDriver](https://chromedriver.chromium.org/)

4. **Set up environment variables**:
   ```bash
   export GEMINI_API_KEY="your-gemini-api-key-here"
   ```

## Usage

### Terminal Interface

Run the interactive terminal interface:

```bash
cd backend
python src/power_plant_search.py
```

Example session:
```
🏭 Power Plant Annual Report Search Engine
==========================================

Enter power plant name (or 'quit' to exit): Palo Verde Nuclear Generating Station

🔍 Searching for annual reports for: Palo Verde Nuclear Generating Station
⏳ This may take a few moments...

🔍 POWER PLANT ANNUAL REPORT SEARCH RESULTS
================================================================================

Power Plant: Palo Verde Nuclear Generating Station
Holding Company: Arizona Public Service Company
Search Phase: Holding Company Search

📄 ANNUAL REPORT FINDINGS:
[Results with specific website URLs and annual report information]

🔗 SOURCES FOUND:
1. Arizona Public Service Company - Investor Relations
   URL: https://www.aps.com/investors

🤖 Would you like to scrape PDFs from these sources? (y/n): y
```

### Testing

Run the test suite with sample power plants:

```bash
cd backend
python test_power_plant_search.py
```

This will test the search engine with several well-known power plants and provide a summary of results.

### Programmatic Usage

```python
from src.agent.graph import power_plant_graph
from src.power_plant_search import initialize_power_plant_state

# Initialize search for a power plant
plant_name = "Diablo Canyon Power Plant"
initial_state = initialize_power_plant_state(plant_name)

# Run the search
final_state = power_plant_graph.invoke(initial_state)

# Access results
sources = final_state.get("sources_gathered", [])
holding_company = final_state.get("holding_company_name", "")
```

## Configuration

The search engine uses the same configuration as the base LangGraph agent:

- **Query Generator Model**: `gemini-2.0-flash` (for generating search queries)
- **Reflection Model**: `gemini-2.5-flash` (for analyzing results)
- **Answer Model**: `gemini-2.5-pro` (for final answer generation)
- **Max Research Loops**: 2 (to prevent infinite searching)
- **Initial Query Count**: 2 (number of initial search queries)

## Architecture

### Key Components

1. **Power Plant Graph** (`src/agent/graph.py`):
   - `generate_power_plant_query`: Creates targeted search queries
   - `power_plant_web_research`: Performs web searches with Google Search API
   - `power_plant_reflection`: Analyzes results and determines next steps
   - `evaluate_power_plant_research`: Routes between direct search and holding company search
   - `power_plant_finalize_answer`: Formats final results

2. **Specialized Prompts** (`src/agent/prompts.py`):
   - `power_plant_query_instructions`: For direct power plant searches
   - `holding_company_search_instructions`: For holding company identification
   - `power_plant_web_search_instructions`: For web research execution
   - `power_plant_reflection_instructions`: For result analysis
   - `power_plant_final_answer_instructions`: For final formatting

3. **State Management** (`src/agent/state.py`):
   - Extended `OverallState` with power plant specific fields
   - Tracks search phase, plant name, holding company, and found URLs

4. **PDF Scraper** (`src/agent/pdf_scraper.py`):
   - Selenium-based PDF discovery and downloading
   - Intelligent filtering for annual report PDFs
   - Organized file storage by power plant

### Search Flow

```
User Input (Plant Name)
         ↓
Generate Power Plant Query
         ↓
Web Research (Direct Search)
         ↓
Reflection & Analysis
         ↓
    [Decision Point]
         ↓
   Found Reports? → Yes → Finalize Answer
         ↓ No
Search for Holding Company
         ↓
Web Research (Holding Company)
         ↓
Reflection & Analysis
         ↓
Finalize Answer
```

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY is not set"**:
   - Ensure you have set the environment variable: `export GEMINI_API_KEY="your-key"`

2. **"Chrome driver not found"**:
   - Install ChromeDriver for your system
   - Ensure Chrome browser is installed

3. **"No sources found"**:
   - Some power plants may not have publicly available annual reports
   - Try variations of the power plant name
   - Check if the plant is owned by a publicly traded company

4. **PDF scraping fails**:
   - Some websites may block automated access
   - PDFs might be behind authentication
   - Try running with `headless=False` to see what's happening

### Debug Mode

To run with more verbose output, modify the terminal interface to show intermediate steps:

```python
# In power_plant_search.py, add debug prints in the main loop
print(f"Debug: Initial state = {initial_state}")
print(f"Debug: Final state keys = {list(final_state.keys())}")
```

## Limitations

1. **Public Information Only**: Can only find publicly available annual reports
2. **English Language**: Optimized for English-language searches
3. **Rate Limiting**: Subject to Google Search API rate limits
4. **PDF Access**: Some PDFs may be behind authentication or paywalls
5. **Dynamic Content**: May not work with heavily JavaScript-dependent sites

## Future Enhancements

- Support for international power plants
- Integration with SEC EDGAR database for US companies
- Support for other document types (sustainability reports, etc.)
- Batch processing for multiple power plants
- Enhanced PDF content analysis
- Integration with document management systems
