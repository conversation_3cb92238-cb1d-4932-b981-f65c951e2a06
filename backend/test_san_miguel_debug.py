#!/usr/bin/env python3
"""
Test script to debug San Miguel PDF scraping issue
"""

import os
import sys
import requests

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.pdf_scraper import PDFScraper


def test_url_resolution():
    """Test URL resolution for San Miguel redirect URLs."""
    print("🔍 Testing San Miguel URL Resolution")
    print("=" * 50)
    
    # Sample redirect URLs from the San Miguel search
    test_urls = [
        "https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUZIYQGHSFXNsWnDVuJBTJ0m6ZPrVkp8YmstX1OHgcCV0vKHQmUn_FF2Hyog9rDsIH61IAAMt25nBqShM9vDQK38UyDRugVMIznVRlgint2ZNgw1jn0ak7oc9rH93EbfBwjLS7JY80e2UBk19zR_JkGXyUZ4TfQuezlQsnpYDp3z8eABKcxteWpydPg1TNp3SI9V01r7-0U57uUivUg=",
        "https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUZIYQGbobDC-_xUFIljR2EEoy_Xc3WrOH1BEP7A43SrLDMiVCT9dkU_KYg5tJAi5B419mp9jQaGr7PGFLkNxPlDg0uvxp5UPXiMv9k85Fx0TWZWeCXuf8tMSAIrf7O0comL6ywFLCcAOoqJYZAOjm_uts_5rk84_VcLbOBMHUTJ-j43DbQC2rnYY4qyI3FALK7V1iODEVMNa_0PSpA=",
        "https://vertexaisearch.cloud.google.com/grounding-api-redirect/AUZIYQGHSFXNsWnDVuJBTJ0m6ZPrVkp8YmstX1OHgcCV0vKHQmUn_FF2Hyog9rDsIH61IAAMt25nBqShM9vDQK38UyDRugVMIznVRlgint2ZNgw1jn0ak7oc9rH93EbfBwjLS7JY80e2UBk19zR_JkGXyUZ4TfQuezlQsnpYDp3z8eABKcxteWpydPg1TNp3SI9V01r7-0U57uUivUg="
    ]
    
    print(f"Testing {len(test_urls)} redirect URLs...")
    
    resolved_urls = []
    for i, url in enumerate(test_urls, 1):
        try:
            print(f"\n{i}. Testing URL: {url[:80]}...")
            
            # Try to resolve the redirect
            response = requests.head(url, allow_redirects=True, timeout=10)
            resolved_url = response.url
            
            print(f"   ✅ Resolved to: {resolved_url}")
            resolved_urls.append(resolved_url)
            
            # Check if it looks like an annual reports page
            if any(keyword in resolved_url.lower() for keyword in ['annual', 'report', 'investor', 'financial']):
                print(f"   ✅ Looks like an annual reports page")
            else:
                print(f"   ⚠️  Doesn't look like an annual reports page")
                
        except Exception as e:
            print(f"   ❌ Failed to resolve: {str(e)}")
    
    return resolved_urls


def test_pdf_scraping(urls):
    """Test PDF scraping on resolved URLs."""
    print(f"\n🔍 Testing PDF Scraping")
    print("=" * 30)
    
    if not urls:
        print("❌ No URLs to test")
        return
    
    # Test each unique URL
    unique_urls = list(set(urls))
    print(f"Testing {len(unique_urls)} unique URLs...")
    
    try:
        scraper = PDFScraper(download_dir="./test_downloads", headless=False)  # Use visible browser for debugging
        
        for i, url in enumerate(unique_urls, 1):
            print(f"\n{i}. Testing PDF scraping on: {url}")
            
            try:
                pdf_links = scraper.find_pdf_links(url)
                print(f"   Found {len(pdf_links)} PDF links")
                
                if pdf_links:
                    for j, pdf_info in enumerate(pdf_links, 1):
                        print(f"      {j}. {pdf_info['filename']}")
                        print(f"         URL: {pdf_info['url'][:80]}...")
                else:
                    print("   ❌ No PDF links found")
                    print("   💡 Try checking the page manually to see if PDFs are there")
                    
            except Exception as e:
                print(f"   ❌ Error scraping {url}: {str(e)}")
        
        scraper.cleanup()
        
    except Exception as e:
        print(f"❌ Error setting up scraper: {str(e)}")


def test_manual_urls():
    """Test known San Miguel annual report URLs."""
    print(f"\n🔍 Testing Known San Miguel URLs")
    print("=" * 40)
    
    # Known San Miguel URLs to test
    known_urls = [
        "https://www.smc.com.ph/investor-relations/annual-reports/",
        "https://www.sanmiguel.com.ph/corporate/investor-relations/financial-performance/annual-reports",
        "https://www.smc.com.ph/",
        "https://www.sanmiguel.com.ph/"
    ]
    
    try:
        scraper = PDFScraper(download_dir="./test_downloads", headless=False)
        
        for url in known_urls:
            print(f"\nTesting: {url}")
            try:
                pdf_links = scraper.find_pdf_links(url)
                print(f"   Found {len(pdf_links)} PDF links")
                
                if pdf_links:
                    for pdf_info in pdf_links[:3]:  # Show first 3
                        print(f"      - {pdf_info['filename']}")
                        
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        scraper.cleanup()
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")


def main():
    """Main test function."""
    print("🐛 San Miguel PDF Scraping Debug")
    print("=" * 40)
    
    # Test 1: URL Resolution
    resolved_urls = test_url_resolution()
    
    # Test 2: PDF Scraping on resolved URLs
    if resolved_urls:
        test_pdf_scraping(resolved_urls)
    
    # Test 3: Manual testing of known URLs
    test_manual_urls()
    
    print(f"\n💡 DEBUGGING TIPS:")
    print(f"   1. Check if the resolved URLs are correct")
    print(f"   2. Manually visit the URLs to see if PDFs are there")
    print(f"   3. Check if the page requires JavaScript to load PDFs")
    print(f"   4. Look for different URL patterns for annual reports")


if __name__ == "__main__":
    main()
