#!/usr/bin/env python3
"""
Test script to verify URL deduplication is working properly
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from power_plant_search import initialize_power_plant_state
from agent.graph import power_plant_graph


def test_url_deduplication():
    """Test that URL deduplication prevents infinite loops."""
    print("🔄 Testing URL Deduplication for SEIL")
    print("=" * 50)
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set.")
        return
    
    try:
        # Initialize state for SEIL
        initial_state = initialize_power_plant_state("SEIL")
        
        # Run the search
        print("🔍 Running search...")
        final_state = power_plant_graph.invoke(initial_state)
        
        # Analyze sources
        sources = final_state.get("sources_gathered", [])
        print(f"\n📊 SOURCES ANALYSIS:")
        print(f"   Total sources: {len(sources)}")
        
        # Check for duplicates
        urls = [source.get('value', '') for source in sources]
        unique_urls = set(urls)
        
        print(f"   Unique URLs: {len(unique_urls)}")
        print(f"   Duplicates: {len(urls) - len(unique_urls)}")
        
        if len(unique_urls) == len(urls):
            print("   ✅ No duplicate URLs found")
        else:
            print("   ⚠️  Duplicate URLs detected")
        
        # Check domains
        domains = []
        for url in urls:
            if "grounding-api-redirect" in url:
                # For redirect URLs, we'll use a placeholder
                domains.append("redirect-url")
            else:
                try:
                    domain = url.split('/')[2] if '/' in url else url
                    domains.append(domain)
                except:
                    domains.append("unknown")
        
        unique_domains = set(domains)
        print(f"   Unique domains: {len(unique_domains)}")
        print(f"   Domain duplicates: {len(domains) - len(unique_domains)}")
        
        # Show domains
        print(f"\n🌐 DOMAINS FOUND:")
        for domain in unique_domains:
            count = domains.count(domain)
            if count > 1:
                print(f"   ⚠️  {domain}: {count} times (duplicate)")
            else:
                print(f"   ✅ {domain}: {count} time")
        
        # Test the deduplication function
        print(f"\n🧪 TESTING DEDUPLICATION FUNCTION:")
        print("   Simulating PDF scraping with deduplication...")
        
        # Create a mock sources list with duplicates
        mock_sources = [
            {"value": "https://www.seilenergy.com/page1", "label": "seilenergy"},
            {"value": "https://www.seilenergy.com/page2", "label": "seilenergy"},
            {"value": "https://www.seilenergy.com/page1", "label": "seilenergy"},  # duplicate
            {"value": "https://tracxn.com/page", "label": "tracxn"},
        ]
        
        # Test deduplication logic
        seen_domains = set()
        unique_sources = []
        
        for source in mock_sources:
            url = source["value"]
            domain = url.split('/')[2] if '/' in url else url
            
            if domain not in seen_domains:
                unique_sources.append(source)
                seen_domains.add(domain)
                print(f"   ✅ Added: {domain}")
            else:
                print(f"   ⏭️  Skipped duplicate: {domain}")
        
        print(f"   Result: {len(mock_sources)} → {len(unique_sources)} sources")
        
        # Overall assessment
        print(f"\n🎯 ASSESSMENT:")
        if len(sources) <= 5 and len(unique_domains) <= 3:
            print("   ✅ EXCELLENT! Good source filtering and deduplication")
        elif len(sources) <= 10:
            print("   ✅ GOOD! Reasonable number of sources")
        else:
            print("   ⚠️  TOO MANY SOURCES! May cause infinite loops")
        
        return final_state
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return {}


if __name__ == "__main__":
    test_url_deduplication()
